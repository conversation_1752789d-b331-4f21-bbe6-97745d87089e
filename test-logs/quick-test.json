{"sessionId": "77f355ad-2006-4b95-acc6-f0e64764e026", "timestamp": "2025-07-07T13:43:57.328Z", "config": {"serverUrl": "localhost", "serverPort": 3000, "maxDelayedWaitTime": 30000, "logLevel": "normal", "outputFile": "/home/<USER>/Workspace/forachat/scripts/../test-logs/quick-test.json", "skipDelayWait": false}, "summary": {"total": 2, "successful": 2, "failed": 0, "totalDuration": 69712}, "results": [{"id": "prompt_1", "prompt": "How do I handle difficult conversations?", "success": true, "response": {"conversationId": 288, "theme": "communication skills", "skills": ["clear communication", "active listening", "empathy", "conflict resolution"], "reply": [], "response": {"reply": [], "theme": "communication skills", "skills": ["clear communication", "active listening", "empathy", "conflict resolution"]}}, "duration": 34929, "timestamp": "2025-07-07T13:42:45.615Z", "conversationId": 288, "messageCount": 0, "delayedMessages": [], "characterReplies": [], "replyAnalysis": {"totalReplies": 0, "immediateReplies": 0, "delayedReplies": 0, "characterBreakdown": {}, "averageDelay": 0, "totalResponseTime": 34929, "theme": "communication skills", "skills": ["clear communication", "active listening", "empathy", "conflict resolution"]}}, {"id": "prompt_2", "prompt": "What's the best way to give feedback?", "success": true, "response": {"conversationId": 288, "theme": "communication skills", "skills": ["Feedback Delivery", "Clear Communication", "Active Listening"], "reply": [], "response": {"reply": [], "theme": "communication skills", "skills": ["Feedback Delivery", "Clear Communication", "Active Listening"]}}, "duration": 34783, "timestamp": "2025-07-07T13:43:22.544Z", "conversationId": 288, "messageCount": 0, "delayedMessages": [], "characterReplies": [], "replyAnalysis": {"totalReplies": 0, "immediateReplies": 0, "delayedReplies": 0, "characterBreakdown": {}, "averageDelay": 0, "totalResponseTime": 34783, "theme": "communication skills", "skills": ["Feedback Delivery", "Clear Communication", "Active Listening"]}}]}