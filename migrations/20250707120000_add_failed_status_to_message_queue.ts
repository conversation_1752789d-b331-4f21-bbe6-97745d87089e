exports.up = function(knex) {
    // PostgreSQL doesn't allow adding values to enums created with table.enum()
    // We need to use raw SQL to modify the check constraint
    return knex.raw(`
        ALTER TABLE forachat.message_queue
        DROP CONSTRAINT IF EXISTS message_queue_status_check;
    `).then(() => {
        return knex.raw(`
            ALTER TABLE forachat.message_queue
            ADD CONSTRAINT message_queue_status_check
            CHECK (status IN ('PENDING', 'PROCESSING', 'VALIDATING', 'SENT', 'CANCELLED', 'WITHDRAWN', 'FAILED'));
        `);
    });
};

exports.down = function(knex) {
    // Revert the check constraint to exclude 'FAILED'
    return knex.raw(`
        ALTER TABLE forachat.message_queue
        DROP CONSTRAINT IF EXISTS message_queue_status_check;
    `).then(() => {
        return knex.raw(`
            ALTER TABLE forachat.message_queue
            ADD CONSTRAINT message_queue_status_check
            CHECK (status IN ('PENDING', 'PROCESSING', 'VALIDATING', 'SENT', 'CANCELLED', 'WITHDRAWN'));
        `);
    });
};
